import pandas as pd
import os

def check_excel_file(filename):
    print(f"\n=== 检查文件: {filename} ===")
    try:
        # 获取所有工作表名称
        excel_file = pd.ExcelFile(filename)
        sheet_names = excel_file.sheet_names
        
        print(f"工作表数量: {len(sheet_names)}")
        
        # 读取第一个工作表
        df = pd.read_excel(filename, sheet_name=sheet_names[0])
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        # 检查关键列是否有数据
        key_columns = ['电话', '媒介', '建档类型']
        has_data = False
        
        for col in key_columns:
            if col in df.columns:
                count = df[col].count()
                print(f"{col}列非空值数量: {count}")
                if count > 0:
                    has_data = True
                    print(f"{col}列前3个值:")
                    print(df[col].dropna().head(3).tolist())
        
        return has_data
        
    except Exception as e:
        print(f"读取文件 {filename} 时出错: {e}")
        return False

def main():
    # 检查几个可能有数据的Excel文件
    files_to_check = [
        '建档信息-2024-01-01到2025-02-13.xlsx',
        '建档信息-2024-01-01到2025-03-31.xlsx',
        '建档信息-2024-01-01到2025-04-12.xlsx',
        '建档信息-2025-01-01到2025-05-30.xlsx'
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            has_data = check_excel_file(filename)
            if has_data:
                print(f"*** 文件 {filename} 包含有效数据 ***")
                break
        else:
            print(f"文件 {filename} 不存在")

if __name__ == "__main__":
    main()
