import pandas as pd

def main():
    # 读取Excel文件
    print("正在读取Excel文件...")
    df = pd.read_excel('建档信息-2024-01-01到2025-02-13.xlsx')
    
    print(f"原始数据行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    
    # 显示所有列名
    print("\n所有列名:")
    for i, col in enumerate(df.columns):
        print(f"{i+1}. {col}")
    
    # 显示前几行数据
    print("\n前5行数据:")
    print(df.head())
    
    # 检查每列的非空值数量
    print("\n每列的非空值数量:")
    non_null_counts = df.count()
    for col in df.columns:
        print(f"{col}: {non_null_counts[col]}")
    
    # 查看是否有其他可能的列名包含"媒介"或"建档"
    print("\n包含'媒介'的列名:")
    for col in df.columns:
        if '媒介' in str(col):
            print(f"  {col}")
    
    print("\n包含'建档'的列名:")
    for col in df.columns:
        if '建档' in str(col):
            print(f"  {col}")
    
    print("\n包含'类型'的列名:")
    for col in df.columns:
        if '类型' in str(col):
            print(f"  {col}")

if __name__ == "__main__":
    main()
