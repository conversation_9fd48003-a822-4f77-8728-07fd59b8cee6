import pandas as pd
from datetime import datetime, timedelta
import os
# 读取Excel文件
df = pd.read_excel('建档信息-2025-01-01到2025-07-21.xlsx')

# 将最后回访时间列转换为datetime类型
df = df.reset_index(drop=True)
df.loc[df['最后回访时间'].isna(), '最后回访时间'] = df['建档时间']
df['最后回访时间'] = pd.to_datetime(df['最后回访时间'])

# 获取当前时间
current_time = datetime.now()

# 筛选条件：
# 1. 最后回访时间非空
# 2. 最后回访时间超过设定天数
# 3. 是否成交为"否"
day_filter=14
mask = (
    (df['最后回访时间'].notna()) & 
    ((current_time - df['最后回访时间']) > timedelta(days=day_filter)) &
    (df['是否成交'] == '否')
)
filtered_df = df[mask]
# 按网电咨询师分组并写入不同的工作表
if not os.path.exists(f'./未成交-{day_filter}天未回访'):
    os.makedirs(f'./未成交-{day_filter}天未回访')
with pd.ExcelWriter(f'./未成交-{day_filter}天未回访/未成交-{day_filter}天未回访.xlsx') as writer:
    filtered_df.to_excel(writer,f'{day_filter}天未回访未成交-总表', index=False)
    for consultant, group in filtered_df.groupby('网电咨询师'):
        # 如果consultant是空值，使用'未分配'作为工作表名
        sheet_name = '未分配' if pd.isna(consultant) else str(consultant)
        # 将数据写入相应的工作表
        group.to_excel(writer,f'{sheet_name}', index=False)

        print(f'处理完成！数据已保存到 {sheet_name}-{day_filter}天未回访.xlsx') 
for consultant, group in filtered_df.groupby('网电咨询师'):
    group.to_excel(f'./未成交-{day_filter}天未回访/{consultant}-{day_filter}天未回访.xlsx', index=False)
