import pandas as pd
import numpy as np
import os
import random

def process_excel_data():
    """
    处理Excel文件，按照媒介列筛选并随机抽取数据
    """
    # 文件路径
    input_file = "未成交-14天未回访/未成交-14天未回访.xlsx"
    output_file = "result.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return
    
    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(input_file)
        
        # 显示文件基本信息
        print(f"原始数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查是否有"媒介"列
        if "媒介" not in df.columns:
            print("错误：文件中没有找到'媒介'列")
            print(f"可用的列名: {list(df.columns)}")
            return
        
        # 显示媒介列的唯一值
        print(f"\n媒介列的唯一值:")
        unique_values = df["媒介"].unique()
        for value in unique_values:
            count = len(df[df["媒介"] == value])
            print(f"  {value}: {count}行")
        
        # 定义筛选条件和抽取数量（注意格式要与实际数据匹配）
        conditions = [
            ("市场 / 市场1部", 7),
            ("市场 / 市场2部", 4),
            ("市场 / 市场3部", 3),
            ("市场 / 市场4部", 3),
            ("市场 / 市场5部", 2)
        ]
        
        # 存储抽取的数据
        extracted_data = []
        extracted_indices = []
        
        print("\n开始筛选和抽取数据:")
        
        for condition, sample_size in conditions:
            # 筛选符合条件的数据
            filtered_df = df[df["媒介"] == condition]
            
            if len(filtered_df) == 0:
                print(f"  {condition}: 没有找到匹配的数据")
                continue
            
            if len(filtered_df) < sample_size:
                print(f"  {condition}: 只有{len(filtered_df)}行数据，少于需要的{sample_size}行，将全部抽取")
                sample_size = len(filtered_df)
            
            # 随机抽取数据
            sampled_df = filtered_df.sample(n=sample_size, random_state=42)
            extracted_data.append(sampled_df)
            extracted_indices.extend(sampled_df.index.tolist())
            
            print(f"  {condition}: 从{len(filtered_df)}行中抽取了{len(sampled_df)}行")
        
        if not extracted_data:
            print("没有抽取到任何数据")
            return
        
        # 合并所有抽取的数据
        result_df = pd.concat(extracted_data, ignore_index=True)
        
        # 保存抽取的数据到result.xlsx
        print(f"\n正在保存抽取的数据到 {output_file}...")
        result_df.to_excel(output_file, index=False)
        print(f"成功保存 {len(result_df)} 行数据到 {output_file}")
        
        # 从原数据中删除抽取的行
        remaining_df = df.drop(extracted_indices)

        # 保存更新后的原文件到临时文件
        print(f"正在保存更新后的数据到 {input_file}...")
        remaining_df.to_excel(input_file, index=False)
        print(f"更新后的数据已保存到 {input_file}，剩余 {len(remaining_df)} 行数据")

        
        # 显示结果摘要
        print("\n处理完成！")
        print(f"抽取的数据保存在: {output_file} ({len(result_df)}行)")
        print(f"更新后的数据保存在: {input_file} ({len(remaining_df)}行)")
        
        # 显示抽取数据的摘要
        print("\n抽取数据按媒介分组统计:")
        for media in result_df["媒介"].unique():
            count = len(result_df[result_df["媒介"] == media])
            print(f"  {media}: {count}行")
            
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    process_excel_data()
