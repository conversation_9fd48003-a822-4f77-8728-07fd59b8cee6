import pandas as pd

def main():
    # 读取Excel文件
    print("正在读取Excel文件...")
    df = pd.read_excel('建档信息-2024-01-01到2025-02-13.xlsx')
    
    print(f"原始数据行数: {len(df)}")
    
    # 查看媒介列的唯一值
    print("\n媒介列的唯一值:")
    if '媒介' in df.columns:
        df['媒介'] = df['媒介'].astype(str)
        unique_meijie = df['媒介'].value_counts()
        print(unique_meijie.head(20))  # 显示前20个最常见的值
        
        # 查看包含"市场"的值
        market_values = df[df['媒介'].str.contains('市场', na=False)]['媒介'].value_counts()
        print(f"\n包含'市场'的媒介值:")
        print(market_values)
    
    # 查看建档类型列的唯一值
    print("\n建档类型列的唯一值:")
    if '建档类型' in df.columns:
        df['建档类型'] = df['建档类型'].astype(str)
        unique_jiandang = df['建档类型'].value_counts()
        print(unique_jiandang.head(20))  # 显示前20个最常见的值
        
        # 查看包含关键词的值
        keywords = ["百度", "抖音", "朋友圈", "快手", "高德", "美团", "美吧", "美呗", "美程", "贝色"]
        pattern = '|'.join(keywords)
        network_values = df[df['建档类型'].str.contains(pattern, na=False)]['建档类型'].value_counts()
        print(f"\n包含网络平台关键词的建档类型值:")
        print(network_values)

if __name__ == "__main__":
    main()
