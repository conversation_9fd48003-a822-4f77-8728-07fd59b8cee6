import pandas as pd
import re
from openpyxl import Workbook
from openpyxl.styles import Font

def clean_phone_number(phone):
    """清理电话号码，去掉字母后缀"""
    if pd.isna(phone):
        return phone
    # 将电话号码转换为字符串
    phone_str = str(phone)
    # 使用正则表达式去掉字母后缀，只保留数字
    cleaned = re.sub(r'[A-Za-z]+$', '', phone_str)
    return cleaned

def main():
    # 读取Excel文件
    print("正在读取Excel文件...")
    # 使用有数据的文件
    df = pd.read_excel('建档信息-2024-01-01到2025-03-31.xlsx')
    
    print(f"原始数据行数: {len(df)}")
    print(f"列名: {list(df.columns)}")
    
    # 筛选媒介列包含"市场"的数据
    print("\n筛选媒介列包含'市场'的数据...")
    if '媒介' in df.columns:
        # 先将媒介列转换为字符串类型
        df['媒介'] = df['媒介'].astype(str)
        df_shichang = df[df['媒介'].str.contains('市场', na=False)].copy()
        print(f"市场数据行数: {len(df_shichang)}")
    else:
        print("警告：未找到'媒介'列")
        df_shichang = pd.DataFrame()

    # 筛选建档类型列包含指定关键词的数据
    print("\n筛选建档类型列包含网络平台关键词的数据...")
    keywords = ["百度", "抖音", "朋友圈", "快手", "高德", "美团", "美吧", "美呗", "美程", "贝色"]
    if '建档类型' in df.columns:
        # 先将建档类型列转换为字符串类型
        df['建档类型'] = df['建档类型'].astype(str)
        pattern = '|'.join(keywords)
        df_wangluo = df[df['建档类型'].str.contains(pattern, na=False)].copy()
        print(f"网络数据行数: {len(df_wangluo)}")
    else:
        print("警告：未找到'建档类型'列")
        df_wangluo = pd.DataFrame()
    
    # 清理电话号码
    print("\n清理电话号码...")
    if '电话' in df_shichang.columns:
        df_shichang['电话_清理'] = df_shichang['电话'].apply(clean_phone_number)
        print(f"市场数据电话清理完成")
    
    if '电话' in df_wangluo.columns:
        df_wangluo['电话_清理'] = df_wangluo['电话'].apply(clean_phone_number)
        print(f"网络数据电话清理完成")
    
    # 找出电话号码相同的行
    print("\n查找电话号码相同的行...")
    if not df_shichang.empty and not df_wangluo.empty and '电话_清理' in df_shichang.columns and '电话_清理' in df_wangluo.columns:
        # 获取两个数据集中电话号码的交集
        common_phones = set(df_shichang['电话_清理'].dropna()) & set(df_wangluo['电话_清理'].dropna())
        print(f"找到相同电话号码: {len(common_phones)} 个")
        
        if common_phones:
            # 筛选出电话号码相同的行
            shichang_matched = df_shichang[df_shichang['电话_清理'].isin(common_phones)].copy()
            wangluo_matched = df_wangluo[df_wangluo['电话_清理'].isin(common_phones)].copy()
            
            # 添加数据来源标识
            shichang_matched['数据来源'] = '市场'
            wangluo_matched['数据来源'] = '网络'
            
            # 合并数据
            result_df = pd.concat([shichang_matched, wangluo_matched], ignore_index=True)
            
            print(f"匹配的市场数据行数: {len(shichang_matched)}")
            print(f"匹配的网络数据行数: {len(wangluo_matched)}")
            print(f"合并后总行数: {len(result_df)}")
            
            # 保存到Excel文件，并设置市场行为红色字体
            print("\n保存结果到Excel文件...")
            
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "筛选结果"
            
            # 写入表头
            headers = list(result_df.columns)
            for col_idx, header in enumerate(headers, 1):
                ws.cell(row=1, column=col_idx, value=header)
            
            # 写入数据并设置格式
            red_font = Font(color="FF0000")  # 红色字体
            
            for row_idx, (_, row) in enumerate(result_df.iterrows(), 2):
                for col_idx, value in enumerate(row, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    # 如果是市场数据，设置红色字体
                    if row['数据来源'] == '市场':
                        cell.font = red_font
            
            # 保存文件
            wb.save('filter_result.xlsx')
            print("结果已保存到 filter_result.xlsx")
            
            # 显示一些统计信息
            print(f"\n统计信息:")
            print(f"相同电话号码数量: {len(common_phones)}")
            print(f"市场数据行数: {len(shichang_matched)}")
            print(f"网络数据行数: {len(wangluo_matched)}")
            print(f"总行数: {len(result_df)}")
            
            # 显示前几个相同的电话号码作为示例
            print(f"\n前5个相同的电话号码示例:")
            for i, phone in enumerate(list(common_phones)[:5], 1):
                print(f"{i}. {phone}")
                
        else:
            print("没有找到相同的电话号码")
    else:
        print("数据为空或缺少必要的列")

if __name__ == "__main__":
    main()
