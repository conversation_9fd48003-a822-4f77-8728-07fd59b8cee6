import pandas as pd

def main():
    # 读取CSV文件
    print("正在读取CSV文件...")
    try:
        df = pd.read_csv('建档信息-2024-01-01到2025-02-13.csv', encoding='utf-8')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv('建档信息-2024-01-01到2025-02-13.csv', encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv('建档信息-2024-01-01到2025-02-13.csv', encoding='latin1')
    
    print(f"原始数据行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    
    # 显示所有列名
    print("\n所有列名:")
    for i, col in enumerate(df.columns):
        print(f"{i+1}. {col}")
    
    # 显示前几行数据
    print("\n前3行数据:")
    print(df.head(3))
    
    # 检查每列的非空值数量
    print("\n每列的非空值数量:")
    non_null_counts = df.count()
    for col in df.columns:
        count = non_null_counts[col]
        if count > 0:  # 只显示有数据的列
            print(f"{col}: {count}")
    
    # 检查关键列
    if '电话' in df.columns:
        phone_count = df['电话'].count()
        print(f"\n电话列非空值数量: {phone_count}")
        if phone_count > 0:
            print("电话列前5个值:")
            print(df['电话'].dropna().head())
    
    if '媒介' in df.columns:
        meijie_count = df['媒介'].count()
        print(f"\n媒介列非空值数量: {meijie_count}")
        if meijie_count > 0:
            print("媒介列前5个值:")
            print(df['媒介'].dropna().head())
    
    if '建档类型' in df.columns:
        jiandang_count = df['建档类型'].count()
        print(f"\n建档类型列非空值数量: {jiandang_count}")
        if jiandang_count > 0:
            print("建档类型列前5个值:")
            print(df['建档类型'].dropna().head())

if __name__ == "__main__":
    main()
