import pandas as pd

def main():
    # 读取Excel文件，查看所有工作表
    print("正在检查Excel文件的工作表...")
    
    # 获取所有工作表名称
    excel_file = pd.ExcelFile('建档信息-2024-01-01到2025-02-13.xlsx')
    sheet_names = excel_file.sheet_names
    
    print(f"工作表数量: {len(sheet_names)}")
    print("工作表名称:")
    for i, sheet in enumerate(sheet_names):
        print(f"{i+1}. {sheet}")
    
    # 读取每个工作表的基本信息
    for sheet_name in sheet_names:
        print(f"\n=== 工作表: {sheet_name} ===")
        try:
            df = pd.read_excel('建档信息-2024-01-01到2025-02-13.xlsx', sheet_name=sheet_name)
            print(f"行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            
            # 显示前几列名
            print("前10列名:")
            for i, col in enumerate(df.columns[:10]):
                print(f"  {i+1}. {col}")
            
            # 检查关键列是否有数据
            if '电话' in df.columns:
                phone_count = df['电话'].count()
                print(f"电话列非空值数量: {phone_count}")
                if phone_count > 0:
                    print("电话列前5个值:")
                    print(df['电话'].dropna().head())
            
            if '媒介' in df.columns:
                meijie_count = df['媒介'].count()
                print(f"媒介列非空值数量: {meijie_count}")
                if meijie_count > 0:
                    print("媒介列前5个值:")
                    print(df['媒介'].dropna().head())
            
            if '建档类型' in df.columns:
                jiandang_count = df['建档类型'].count()
                print(f"建档类型列非空值数量: {jiandang_count}")
                if jiandang_count > 0:
                    print("建档类型列前5个值:")
                    print(df['建档类型'].dropna().head())
                    
        except Exception as e:
            print(f"读取工作表 {sheet_name} 时出错: {e}")

if __name__ == "__main__":
    main()
