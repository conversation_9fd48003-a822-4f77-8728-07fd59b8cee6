import pandas as pd

def main():
    # 读取结果文件
    print("正在验证结果文件...")
    df = pd.read_excel('filter_result.xlsx')
    
    print(f"结果文件行数: {len(df)}")
    print(f"结果文件列数: {len(df.columns)}")
    
    # 显示列名
    print("\n列名:")
    for i, col in enumerate(df.columns):
        print(f"{i+1}. {col}")
    
    # 显示数据
    print("\n所有数据:")
    print(df.to_string())
    
    # 检查数据来源分布
    if '数据来源' in df.columns:
        print(f"\n数据来源分布:")
        print(df['数据来源'].value_counts())
    
    # 检查电话号码
    if '电话_清理' in df.columns:
        print(f"\n清理后的电话号码:")
        print(df['电话_清理'].tolist())
    
    if '电话' in df.columns:
        print(f"\n原始电话号码:")
        print(df['电话'].tolist())

if __name__ == "__main__":
    main()
